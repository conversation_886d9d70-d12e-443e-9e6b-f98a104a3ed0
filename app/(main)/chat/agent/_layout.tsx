import { Tabs } from 'expo-router'

import TestIcon from '@/assets/icons/3d-cube-scan-icon.svg'

const ChatAgentScreen = () => {
  return (
    <Tabs>
      <Tabs.Screen
        name="index" // Route: /wallet
        options={{
          title: 'Tiền Mặt',
          tabBarIcon: ({ color }) => <TestIcon color={color} />,
        }}
      />
      <Tabs.Screen
        name="inbox" // Route: /wallet/cards
        options={{
          title: 'Thẻ Ngân Hàng',
          tabBarIcon: ({ color }) => <TestIcon color={color} />,
        }}
      />
    </Tabs>
  )
}

export default ChatAgentScreen
